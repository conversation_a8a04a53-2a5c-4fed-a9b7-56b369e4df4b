# Financial Report Extraction Module

This module contains all the logic for extracting financial and annual report website links from power plant searches. It provides a clean separation of concerns for the financial report discovery and link extraction functionality.

## Overview

The financial report extraction module consolidates logic that was previously scattered across multiple files in the codebase. It focuses specifically on the task of finding and extracting website links that are likely to contain annual reports and financial information for power plants and their holding companies.

## Architecture

The module is organized into four main components:

### 1. WebsiteLinkExtractor (`website_link_extractor.py`)
The main orchestrator class that coordinates the entire link extraction pipeline:
- Extracts URLs from AI-generated search answers
- Extracts URLs from search result sources  
- Prioritizes AI-identified URLs over source URLs
- Resolves redirects and cleans URLs
- Filters and ranks URLs based on content analysis
- Generates alternative URL patterns for common annual report locations

### 2. ContentAnalyzer (`content_analyzer.py`)
Analyzes webpage content to determine relevance for annual reports:
- Scores webpages based on financial report indicators
- Handles both direct requests and ScraperAPI for JavaScript-heavy sites
- Finds PDF links on relevant pages
- Uses configurable indicator patterns (strong, medium, weak, negative)

### 3. URLResolver (`url_resolver.py`)
Handles URL resolution and cleaning:
- Follows redirects to get final destination URLs
- Specifically handles Google redirect URLs
- Cleans tracking parameters from URLs
- Validates URL accessibility
- Provides domain extraction and comparison utilities

### 4. LinkFilter (`link_filter.py`)
Filters and prioritizes links based on relevance:
- Filters URLs based on financial report patterns
- Deduplicates URLs by domain
- Prioritizes URLs based on domain authority and patterns
- Removes exact duplicates
- Provides domain distribution analysis

## Usage

### Basic Usage

```python
from financial_report_extraction import WebsiteLinkExtractor

# Initialize the extractor
extractor = WebsiteLinkExtractor()

# Extract links from search results
final_state = {...}  # Search results from agent
prioritized_urls, all_urls = extractor.extract_links_from_search_results(
    final_state, 
    max_results=3
)

# Generate alternative URLs if needed
alternative_urls = extractor.generate_alternative_urls(prioritized_urls)
```

### Individual Component Usage

```python
from financial_report_extraction import ContentAnalyzer, URLResolver, LinkFilter

# Analyze content of specific URLs
analyzer = ContentAnalyzer()
analysis = analyzer.analyze_url_content("https://example.com/investor-relations")

# Resolve redirects
resolver = URLResolver()
resolved_url = resolver.resolve_redirect_url("https://google.com/url?q=...")

# Filter and prioritize links
filter = LinkFilter()
relevant_urls = filter.filter_relevant_urls(url_list)
prioritized_urls = filter.prioritize_urls(relevant_urls)
```

## Key Features

### Smart URL Prioritization
- AI-identified URLs from search answers get highest priority
- Content analysis scores pages based on financial report indicators
- Domain-based deduplication prevents duplicate sources
- Alternative URL generation for common annual report patterns

### Robust URL Handling
- Handles Google redirect URLs and other redirect chains
- Cleans tracking parameters and normalizes URLs
- Validates URL accessibility before processing
- Graceful error handling for network issues

### Configurable Filtering
- Configurable indicator patterns for different types of content
- Domain-based filtering and prioritization
- Negative pattern detection to avoid irrelevant pages
- Flexible scoring system for URL relevance

### ScraperAPI Integration
- Falls back to ScraperAPI for JavaScript-heavy websites
- Handles sites that require browser rendering
- Configurable API usage to manage costs

## Configuration

The module uses environment variables for configuration:

- `SCRAPER_API_KEY`: API key for ScraperAPI service (optional)

## Integration with Existing Code

This module is designed to replace the scattered link extraction logic in:
- `power_plant_search.py` (URL extraction and processing)
- `agent/content_analyzer.py` (content analysis logic)
- Various utility functions for URL handling

### Migration Path

1. Import the new module: `from financial_report_extraction import WebsiteLinkExtractor`
2. Replace existing URL extraction logic with `extractor.extract_links_from_search_results()`
3. Use the returned prioritized URLs for PDF scraping
4. Remove old scattered logic from other files

## Error Handling

The module includes comprehensive error handling:
- Network timeouts and connection errors
- Invalid URL formats
- Content parsing errors
- API rate limiting and failures

All errors are logged with descriptive messages and the system gracefully degrades to fallback methods.

## Future Enhancements

Potential areas for future improvement:
- Machine learning-based content relevance scoring
- Company-specific URL pattern learning
- Enhanced PDF detection on pages
- Caching of content analysis results
- Integration with additional web scraping services
