"""
Content Analyzer

Analyzes webpage content to determine if it contains annual report or financial information.
This is extracted and refactored from the original agent/content_analyzer.py to focus
specifically on the financial report link extraction logic.
"""

import os
import requests
from typing import List, Dict, Optional
from bs4 import BeautifulSoup
from urllib.parse import urljoin, urlparse


class ContentAnalyzer:
    """
    Analyzes webpage content for annual report and financial information indicators.
    """
    
    def __init__(self):
        self.scraper_api_key = os.getenv('SCRAPER_API_KEY')
        
        # Strong indicators for annual report content
        self.strong_indicators = [
            'annual report', 'annual reports', 'yearly report', 'yearly reports',
            'form 10-k', '10-k filing', 'sec filing', 'sec filings',
            'financial statements', 'financial reports', 'investor relations',
            'annual financial report', 'corporate annual report'
        ]
        
        # Medium indicators
        self.medium_indicators = [
            'quarterly report', 'earnings report', 'financial information',
            'corporate governance', 'sustainability report', 'esg report',
            'investor', 'shareholders', 'financial data'
        ]
        
        # Weak indicators
        self.weak_indicators = [
            'report', 'financial', 'corporate', 'company information',
            'about us', 'corporate information'
        ]
        
        # Negative indicators (reduce relevance)
        self.negative_indicators = [
            'news', 'press release', 'blog', 'career', 'job',
            'contact', 'privacy policy', 'terms of service'
        ]
    
    def analyze_url_content(self, url: str, use_scraper_api: bool = False) -> Dict[str, any]:
        """
        Analyze a single URL for annual report content.
        
        Args:
            url: URL to analyze
            use_scraper_api: Whether to use ScraperAPI for JavaScript-heavy sites
            
        Returns:
            Dictionary with analysis results
        """
        print(f"🔍 Analyzing content of: {url}")
        
        try:
            # Get page content
            if use_scraper_api and self.scraper_api_key:
                content = self._get_content_via_scraper_api(url)
            else:
                content = self._get_content_direct(url)
            
            if not content:
                return {"relevant": False, "reason": "Could not fetch content", "url": url}
            
            # Parse content
            soup = BeautifulSoup(content, 'html.parser')
            
            # Analyze content for annual report indicators
            analysis = self._analyze_content(soup, url)
            analysis["url"] = url
            
            # Find PDF links if this looks like an annual report page
            if analysis["relevant"]:
                pdf_links = self._find_pdf_links(soup, url)
                analysis["pdf_links"] = pdf_links
                analysis["pdf_count"] = len(pdf_links)
            else:
                analysis["pdf_links"] = []
                analysis["pdf_count"] = 0
            
            return analysis
            
        except Exception as e:
            print(f"   ❌ Error analyzing {url}: {str(e)}")
            return {"relevant": False, "reason": f"Analysis error: {str(e)}", "url": url}
    
    def analyze_multiple_urls(self, urls: List[str], use_scraper_api: bool = False) -> List[Dict[str, any]]:
        """
        Analyze multiple URLs for annual report content.
        
        Args:
            urls: List of URLs to analyze
            use_scraper_api: Whether to use ScraperAPI
            
        Returns:
            List of analysis results
        """
        results = []
        
        for url in urls:
            analysis = self.analyze_url_content(url, use_scraper_api)
            results.append(analysis)
        
        return results
    
    def get_best_annual_report_urls(self, urls: List[str], max_results: int = 3) -> List[str]:
        """
        Get the best annual report URLs from a list of candidates.
        
        Args:
            urls: List of candidate URLs
            max_results: Maximum number of URLs to return
            
        Returns:
            List of best URLs for annual reports
        """
        print(f"\n🔍 Analyzing {len(urls)} URLs for annual report content...")
        
        # First try without ScraperAPI (faster)
        results = self.analyze_multiple_urls(urls, use_scraper_api=False)
        
        # If no good results, try with ScraperAPI for JavaScript sites
        relevant_results = [r for r in results if r["relevant"]]
        if not relevant_results and self.scraper_api_key:
            print("⚠️ No relevant content found with direct requests, trying ScraperAPI...")
            results = self.analyze_multiple_urls(urls[:5], use_scraper_api=True)  # Limit to 5 for API costs
            relevant_results = [r for r in results if r["relevant"]]
        
        if not relevant_results:
            print("❌ No relevant annual report content found")
            return []
        
        # Sort by relevance score (descending)
        relevant_results.sort(key=lambda x: x.get("score", 0), reverse=True)
        
        # Return top URLs
        best_urls = [r["url"] for r in relevant_results[:max_results]]
        
        print(f"✅ Found {len(best_urls)} relevant URLs:")
        for i, result in enumerate(relevant_results[:max_results]):
            print(f"   {i+1}. {result['url']} (score: {result.get('score', 0):.2f})")
        
        return best_urls
    
    def _get_content_via_scraper_api(self, url: str) -> Optional[str]:
        """Get webpage content using ScraperAPI."""
        try:
            api_url = f"http://api.scraperapi.com?api_key={self.scraper_api_key}&url={url}"
            response = requests.get(api_url, timeout=30)
            response.raise_for_status()
            return response.text
        except Exception as e:
            print(f"   ⚠️ ScraperAPI failed: {str(e)}")
            return None
    
    def _get_content_direct(self, url: str) -> Optional[str]:
        """Get webpage content with direct request."""
        try:
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
            }
            response = requests.get(url, headers=headers, timeout=15)
            response.raise_for_status()
            return response.text
        except Exception as e:
            print(f"   ⚠️ Direct request failed: {str(e)}")
            return None
    
    def _analyze_content(self, soup: BeautifulSoup, url: str) -> Dict[str, any]:
        """Analyze webpage content for annual report indicators."""
        
        # Get all text content
        page_text = soup.get_text().lower()
        page_title = soup.title.string.lower() if soup.title else ""
        
        # Calculate relevance score
        score = 0
        reasons = []
        
        # Check URL for indicators
        url_lower = url.lower()
        if any(indicator in url_lower for indicator in ['investor', 'annual', 'report', 'financial']):
            score += 2
            reasons.append("URL contains relevant keywords")
        
        # Check title
        title_score = 0
        for indicator in self.strong_indicators:
            if indicator in page_title:
                title_score += 3
                reasons.append(f"Title contains '{indicator}'")
        
        for indicator in self.medium_indicators:
            if indicator in page_title:
                title_score += 2
                reasons.append(f"Title contains '{indicator}'")
        
        score += min(title_score, 10)  # Cap title score
        
        # Check page content
        content_score = 0
        for indicator in self.strong_indicators:
            count = page_text.count(indicator)
            if count > 0:
                content_score += count * 2
                reasons.append(f"Content mentions '{indicator}' {count} times")
        
        for indicator in self.medium_indicators:
            count = page_text.count(indicator)
            if count > 0:
                content_score += count * 1
                reasons.append(f"Content mentions '{indicator}' {count} times")
        
        score += min(content_score, 15)  # Cap content score
        
        # Apply negative indicators
        for indicator in self.negative_indicators:
            if indicator in page_text:
                score -= 1
                reasons.append(f"Negative indicator: '{indicator}'")
        
        # Determine if relevant (threshold: 3)
        relevant = score >= 3
        
        return {
            "relevant": relevant,
            "score": score,
            "reasons": reasons,
            "title": soup.title.string if soup.title else "No title"
        }
    
    def _find_pdf_links(self, soup: BeautifulSoup, base_url: str) -> List[Dict[str, str]]:
        """Find PDF links on the webpage."""
        pdf_links = []
        
        # Find all links that might be PDFs
        links = soup.find_all('a', href=True)
        
        for link in links:
            href = link.get('href', '')
            text = link.get_text(strip=True)
            
            # Check if this looks like a PDF link
            if '.pdf' in href.lower() or 'pdf' in href.lower():
                # Convert relative URLs to absolute
                absolute_url = urljoin(base_url, href)
                
                pdf_info = {
                    "url": absolute_url,
                    "text": text,
                    "href": href
                }
                pdf_links.append(pdf_info)
        
        return pdf_links
