"""
Example Usage of Financial Report Extraction Module

This script demonstrates how to use the financial report extraction module
to extract and process financial report website links.
"""

import os
import sys
from typing import Dict, List

# Add the src directory to the path so we can import the module
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from financial_report_extraction import WebsiteLinkExtractor, ContentAnalyzer, URLResolver, LinkFilter


def example_search_results() -> Dict:
    """
    Example search results structure that would come from the agent.
    This simulates the final_state that contains search results.
    """
    return {
        "messages": [
            {
                "content": """Based on my search, I found several relevant sources for PLTU Suparma annual reports:

1. **San Miguel Corporation Investor Relations**: https://www.sanmiguel.com.ph/investor-relations/
   - This is the main investor relations page for San Miguel Corporation, which owns PLTU Suparma
   - Contains annual reports, financial statements, and SEC filings

2. **SMC Annual Reports Page**: https://www.smc.com.ph/investor-relations/annual-reports/
   - Direct link to annual reports section
   - Contains downloadable PDF annual reports for recent years

3. **SEC EDGAR Database**: https://www.sec.gov/edgar/browse/?CIK=0001234567
   - Official SEC filings for San Miguel Corporation
   - Contains Form 10-K and other regulatory filings

The annual reports contain detailed information about PLTU Suparma's operations, financial performance, and environmental compliance."""
            }
        ],
        "sources": [
            {
                "url": "https://grounding-api-redirect.com/redirect?url=https%3A//www.sanmiguel.com.ph/investor-relations/",
                "title": "San Miguel Corporation - Investor Relations"
            },
            {
                "url": "https://grounding-api-redirect.com/redirect?url=https%3A//www.smc.com.ph/investor-relations/annual-reports/",
                "title": "Annual Reports - SMC"
            },
            {
                "url": "https://grounding-api-redirect.com/redirect?url=https%3A//www.sec.gov/edgar/browse/%3FCIK%3D0001234567",
                "title": "SEC EDGAR - San Miguel Corporation"
            }
        ]
    }


def demonstrate_full_extraction():
    """Demonstrate the complete link extraction pipeline."""
    print("🚀 Financial Report Extraction Demo")
    print("=" * 50)
    
    # Initialize the main extractor
    extractor = WebsiteLinkExtractor()
    
    # Get example search results
    search_results = example_search_results()
    
    print("\n📊 Input Search Results:")
    print(f"   Messages: {len(search_results['messages'])}")
    print(f"   Sources: {len(search_results['sources'])}")
    
    # Extract links using the main pipeline
    print("\n🔗 Extracting Financial Report Links...")
    prioritized_urls, all_urls = extractor.extract_links_from_search_results(
        search_results, 
        max_results=3
    )
    
    print(f"\n✅ Extraction Results:")
    print(f"   Prioritized URLs: {len(prioritized_urls)}")
    print(f"   Total URLs found: {len(all_urls)}")
    
    print(f"\n📋 Prioritized URLs for PDF Scraping:")
    for i, url in enumerate(prioritized_urls, 1):
        print(f"   {i}. {url}")
    
    # Generate alternative URLs
    print(f"\n🔄 Generating Alternative URLs...")
    alternative_urls = extractor.generate_alternative_urls(prioritized_urls)
    
    if alternative_urls:
        print(f"   Found {len(alternative_urls)} alternative URLs:")
        for i, url in enumerate(alternative_urls, 1):
            print(f"   {i}. {url}")
    else:
        print("   No alternative URLs generated")
    
    return prioritized_urls, all_urls, alternative_urls


def demonstrate_individual_components():
    """Demonstrate using individual components separately."""
    print("\n🔧 Individual Component Demos")
    print("=" * 50)
    
    # Example URLs to work with
    test_urls = [
        "https://www.sanmiguel.com.ph/investor-relations/",
        "https://www.smc.com.ph/investor-relations/annual-reports/",
        "https://www.sec.gov/edgar/browse/?CIK=0001234567"
    ]
    
    # 1. URL Resolver Demo
    print("\n🔄 URL Resolver Demo:")
    resolver = URLResolver()
    
    for url in test_urls:
        resolved = resolver.resolve_redirect_url(url)
        print(f"   {url[:50]}... → {resolved[:50] if resolved else 'Failed'}...")
    
    # 2. Link Filter Demo
    print("\n🔍 Link Filter Demo:")
    link_filter = LinkFilter()
    
    # Filter relevant URLs
    relevant_urls = link_filter.filter_relevant_urls(test_urls)
    print(f"   Relevant URLs: {len(relevant_urls)}")
    
    # Prioritize URLs
    prioritized = link_filter.prioritize_urls(relevant_urls)
    print(f"   Prioritized order:")
    for i, url in enumerate(prioritized, 1):
        score = link_filter._calculate_url_score(url)
        print(f"     {i}. {url[:60]}... (score: {score:.1f})")
    
    # 3. Content Analyzer Demo
    print("\n🧠 Content Analyzer Demo:")
    analyzer = ContentAnalyzer()
    
    # Note: This would make actual HTTP requests, so we'll just show the structure
    print("   Would analyze content of URLs for annual report indicators...")
    print("   Example analysis structure:")
    example_analysis = {
        "relevant": True,
        "score": 8.5,
        "reasons": ["URL contains 'investor'", "Content mentions 'annual report' 3 times"],
        "title": "Investor Relations - San Miguel Corporation",
        "pdf_links": [],
        "pdf_count": 0
    }
    
    for key, value in example_analysis.items():
        print(f"     {key}: {value}")
    
    # Cleanup
    resolver.cleanup()


def demonstrate_error_handling():
    """Demonstrate error handling capabilities."""
    print("\n⚠️  Error Handling Demo")
    print("=" * 50)
    
    extractor = WebsiteLinkExtractor()
    
    # Test with invalid search results
    invalid_results = {"messages": [], "sources": []}
    
    print("\n🧪 Testing with empty search results:")
    try:
        urls, all_urls = extractor.extract_links_from_search_results(invalid_results)
        print(f"   Result: {len(urls)} URLs extracted (graceful handling)")
    except Exception as e:
        print(f"   Error: {str(e)}")
    
    # Test URL resolver with invalid URLs
    print("\n🧪 Testing URL resolver with invalid URLs:")
    resolver = URLResolver()
    
    invalid_urls = [
        "not-a-url",
        "https://nonexistent-domain-12345.com",
        "https://httpstat.us/404"
    ]
    
    for url in invalid_urls:
        try:
            resolved = resolver.resolve_redirect_url(url)
            print(f"   {url} → {resolved if resolved else 'Failed (graceful)'}")
        except Exception as e:
            print(f"   {url} → Error: {str(e)}")
    
    resolver.cleanup()


def main():
    """Run all demonstrations."""
    print("Financial Report Extraction Module - Example Usage")
    print("=" * 60)
    
    try:
        # Main extraction demo
        prioritized_urls, all_urls, alternative_urls = demonstrate_full_extraction()
        
        # Individual component demos
        demonstrate_individual_components()
        
        # Error handling demo
        demonstrate_error_handling()
        
        print("\n✅ Demo completed successfully!")
        print("\nNext steps:")
        print("1. Use the prioritized URLs for PDF scraping")
        print("2. Try alternative URLs if primary URLs don't yield results")
        print("3. Integrate this module into your power plant search pipeline")
        
    except Exception as e:
        print(f"\n❌ Demo failed with error: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
